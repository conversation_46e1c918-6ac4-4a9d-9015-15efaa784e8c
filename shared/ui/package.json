{"name": "@v/shared-ui", "private": true, "version": "1.0.9", "type": "module", "homepage": "https://tambia1.github.io", "scripts": {"start": "vite --host --port 8080", "build": "tsc && vite build", "preview": "vite preview --port 8081", "test": "vitest", "coverage": "vitest watch --coverage", "deploy": "gh-pages -d ./dist", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "prepack": "json -f package.json -I -e \"delete this.devDependencies; delete this.dependencies\""}, "dependencies": {"@tanstack/react-query": "5.22.2", "i18next": "23.10.0", "i18next-browser-languagedetector": "7.2.0", "localforage": "1.10.0", "match-sorter": "6.3.4", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "14.0.5", "react-modal": "3.16.1", "react-router-dom": "6.22.1", "react-svg": "16.1.33", "styled-components": "5.3.6", "zustand": "4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "6.4.2", "@testing-library/react": "14.2.1", "@types/node": "20.11.20", "@types/react": "18.2.58", "@types/react-dom": "18.2.19", "@types/styled-components": "5.1.34", "@typescript-eslint/eslint-plugin": "7.0.2", "@typescript-eslint/parser": "7.0.2", "@vitejs/plugin-react": "4.2.1", "@vitest/coverage-v8": "1.3.1", "babel-plugin-styled-components": "2.1.4", "dotenv": "16.4.5", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-react": "7.33.2", "eslint-plugin-react-hooks": "4.6.0", "gh-pages": "6.1.1", "jsdom": "24.0.0", "styled-components": "6.1.8", "typescript": "5.3.3", "vite": "^5.4.1", "vite-plugin-checker": "0.6.4", "vite-plugin-dts": "^4.0.3", "vitest": "1.3.1", "zustand": "4.5.1"}, "files": ["dist"], "module": "./dist/lib.es.js", "main": "./dist/lib.umd.cjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/lib.es.js", "require": "./dist/lib.umd.js"}}}