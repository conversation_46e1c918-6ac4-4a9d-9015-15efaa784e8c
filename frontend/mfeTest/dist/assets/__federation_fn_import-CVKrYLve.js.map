{"version": 3, "mappings": ";AAAA,MAAM,kBAAkB;AACxB,MAAM,QAAQ,UAAU,eAAe,SAAS,eAAe;AAC/D,MAAM,oBAAoB;AAC1B,MAAM,yBAAyB;AAC/B,MAAM,uBAAuB;AAC7B,MAAM,4BAA4B,MAAM,sBAAsB,IAAI,oBAAoB;AACtF,MAAM,kBAAkB,SAAS,yBAAyB,SAAS,yBAAyB;AAC5F,MAAM,uBAAuB,MAAM,iBAAiB,IAAI,oBAAoB;AAC5E,MAAM,aAAa,QAAQ,oBAAoB,SAAS,oBAAoB;AAC5E,MAAM,mBAAmB,GAAG,iBAAiB;AAC7C,MAAM,cAAc,YAAY,gBAAgB,WAAW,gBAAgB,WAAW,gBAAgB,OAAO,UAAU,KAAK,KAAK;AACjI,MAAM,cAAc,SAAS,WAAW,cAAc,WAAW;AACjE,MAAM,mBAAmB,IAAI,sBAAsB,QAAQ,sBAAsB,QAAQ,sBAAsB;AAC/G,MAAM,aAAa,WAAW,gBAAgB,GAAG,eAAe,IAAI,KAAK;AACzE,MAAM,OAAO;AACb,MAAM,iBAAiB,SAAS,IAAI,QAAQ,UAAU,IAAI,WAAW;AACrE,MAAM,YAAY;AAClB,MAAM,YAAY,SAAS,SAAS;AACpC,MAAM,YAAY;AAClB,MAAM,YAAY,SAAS,SAAS;AACpC,MAAM,OAAO;AACb,MAAM,QAAQ,IAAI,SAAS,GAAG,WAAW;AACzC,MAAM,cAAc,IAAI,iBAAiB,QAAQ,iBAAiB,QAAQ,iBAAiB;AAC3F,MAAM,YAAY,KAAK,WAAW,GAAG,UAAU,IAAI,KAAK;AACxD,MAAM,QAAQ,IAAI,SAAS,GAAG,WAAW;AACzC,MAAM,SAAS,IAAI,IAAI,OAAO,WAAW;AACzC,MAAM,aAAa,IAAI,IAAI,QAAQ,SAAS;AAC5C,MAAM,OAAO;AACb,SAAS,WAAW,QAAQ;AAC1B,SAAO,IAAI,OAAO,MAAM;AAC1B;AACA,SAAS,WAAW,SAAS;AAC3B,SAAO,CAAC,WAAW,QAAQ,YAAa,MAAK,OAAO,YAAY;AAClE;AACA,SAAS,QAAQ,KAAK;AACpB,SAAO,CAAC,MAAM;AACZ,WAAO,IAAI,OAAO,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC;AAAA,EACvC;AACA;AACA,SAAS,kBAAkB,kBAAkB;AAC3C,SAAO,iBAAiB,MAAM,WAAW,UAAU,CAAC;AACtD;AACA,SAAS,eAAe,OAAO,OAAO,OAAO,aAAa;AACxD,QAAM,eAAe,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK;AAC/C,MAAI,aAAa;AACf,WAAO,GAAG,YAAY,IAAI,WAAW;AAAA,EACtC;AACD,SAAO;AACT;AACA,SAAS,YAAY,OAAO;AAC1B,SAAO,MAAM;AAAA,IACX,WAAW,WAAW;AAAA,IACtB,CAAC,QAAQ,MAAM,WAAW,WAAW,WAAW,iBAAiB,YAAY,IAAI,SAAS,SAAS,SAAS,iBAAiB;AAC3H,UAAI,WAAW,SAAS,GAAG;AACzB,eAAO;AAAA,MACf,WAAiB,WAAW,SAAS,GAAG;AAChC,eAAO,KAAK,SAAS;AAAA,MAC7B,WAAiB,WAAW,SAAS,GAAG;AAChC,eAAO,KAAK,SAAS,IAAI,SAAS;AAAA,MAC1C,OAAa;AACL,eAAO,KAAK,IAAI;AAAA,MACjB;AACD,UAAI,WAAW,OAAO,GAAG;AACvB,aAAK;AAAA,MACb,WAAiB,WAAW,OAAO,GAAG;AAC9B,aAAK,IAAI,CAAC,UAAU,CAAC;AAAA,MAC7B,WAAiB,WAAW,OAAO,GAAG;AAC9B,aAAK,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC;AAAA,MACjC,WAAU,cAAc;AACvB,aAAK,KAAK,OAAO,IAAI,OAAO,IAAI,OAAO,IAAI,YAAY;AAAA,MAC/D,OAAa;AACL,aAAK,KAAK,EAAE;AAAA,MACb;AACD,aAAO,GAAG,IAAI,IAAI,EAAE,GAAG;IACxB;AAAA,EACL;AACA;AACA,SAAS,oBAAoB,OAAO;AAClC,SAAO,MAAM,QAAQ,WAAW,cAAc,GAAG,QAAQ;AAC3D;AACA,SAAS,eAAe,OAAO;AAC7B,SAAO,MAAM,QAAQ,WAAW,SAAS,GAAG,KAAK;AACnD;AACA,SAAS,eAAe,OAAO;AAC7B,SAAO,MAAM,QAAQ,WAAW,SAAS,GAAG,KAAK;AACnD;AACA,SAAS,YAAY,OAAO;AAC1B,SAAO,MAAM,OAAO,MAAM,KAAK,EAAE,IAAI,CAAC,iBAAiB;AACrD,WAAO,aAAa;AAAA,MAClB,WAAW,KAAK;AAAA,MAChB,CAAC,GAAG,OAAO,OAAO,OAAO,gBAAgB;AACvC,YAAI,WAAW,KAAK,GAAG;AACrB,iBAAO;AAAA,QACjB,WAAmB,WAAW,KAAK,GAAG;AAC5B,iBAAO,KAAK,KAAK,SAAS,CAAC,QAAQ,CAAC;AAAA,QAC9C,WAAmB,WAAW,KAAK,GAAG;AAC5B,cAAI,UAAU,KAAK;AACjB,mBAAO,KAAK,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI,CAAC,QAAQ,CAAC;AAAA,UAChE,OAAiB;AACL,mBAAO,KAAK,KAAK,IAAI,KAAK,OAAO,CAAC,QAAQ,CAAC;AAAA,UAC5C;AAAA,QACF,WAAU,aAAa;AACtB,cAAI,UAAU,KAAK;AACjB,gBAAI,UAAU,KAAK;AACjB,qBAAO,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,WAAW,KAAK,KAAK,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC;AAAA,YACjG,OAAmB;AACL,qBAAO,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,WAAW,KAAK,KAAK,IAAI,CAAC,QAAQ,CAAC;AAAA,YAC3E;AAAA,UACb,OAAiB;AACL,mBAAO,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,WAAW,KAAK,CAAC,QAAQ,CAAC;AAAA,UAClE;AAAA,QACX,OAAe;AACL,cAAI,UAAU,KAAK;AACjB,gBAAI,UAAU,KAAK;AACjB,qBAAO,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC;AAAA,YAClF,OAAmB;AACL,qBAAO,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,CAAC,QAAQ,CAAC;AAAA,YAC5D;AAAA,UACF;AACD,iBAAO,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,CAAC,QAAQ,CAAC;AAAA,QACnD;AAAA,MACF;AAAA,IACP;AAAA,EACA,CAAG,EAAE,KAAK,GAAG;AACb;AACA,SAAS,YAAY,OAAO;AAC1B,SAAO,MAAM,OAAO,MAAM,KAAK,EAAE,IAAI,CAAC,iBAAiB;AACrD,WAAO,aAAa;AAAA,MAClB,WAAW,KAAK;AAAA,MAChB,CAAC,GAAG,OAAO,OAAO,OAAO,gBAAgB;AACvC,YAAI,WAAW,KAAK,GAAG;AACrB,iBAAO;AAAA,QACjB,WAAmB,WAAW,KAAK,GAAG;AAC5B,iBAAO,KAAK,KAAK,SAAS,CAAC,QAAQ,CAAC;AAAA,QAC9C,WAAmB,WAAW,KAAK,GAAG;AAC5B,iBAAO,KAAK,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI,CAAC,QAAQ,CAAC;AAAA,QACrD,WAAU,aAAa;AACtB,iBAAO,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,WAAW,KAAK,KAAK,IAAI,CAAC,QAAQ,CAAC;AAAA,QAC3E;AACD,eAAO,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,CAAC,QAAQ,CAAC;AAAA,MAC5D;AAAA,IACP;AAAA,EACA,CAAG,EAAE,KAAK,GAAG;AACb;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,MAAM,MAAM,KAAK,EAAE,IAAI,CAAC,iBAAiB;AAC9C,WAAO,aAAa,KAAI,EAAG;AAAA,MACzB,WAAW,MAAM;AAAA,MACjB,CAAC,KAAK,OAAO,OAAO,OAAO,OAAO,gBAAgB;AAChD,cAAM,WAAW,WAAW,KAAK;AACjC,cAAM,WAAW,YAAY,WAAW,KAAK;AAC7C,cAAM,WAAW,YAAY,WAAW,KAAK;AAC7C,YAAI,UAAU,OAAO,UAAU;AAC7B,kBAAQ;AAAA,QACT;AACD,sBAAc;AACd,YAAI,UAAU;AACZ,cAAI,UAAU,OAAO,UAAU,KAAK;AAClC,mBAAO;AAAA,UACnB,OAAiB;AACL,mBAAO;AAAA,UACR;AAAA,QACX,WAAmB,SAAS,UAAU;AAC5B,cAAI,UAAU;AACZ,oBAAQ;AAAA,UACT;AACD,kBAAQ;AACR,cAAI,UAAU,KAAK;AACjB,oBAAQ;AACR,gBAAI,UAAU;AACZ,sBAAQ,CAAC,QAAQ;AACjB,sBAAQ;AACR,sBAAQ;AAAA,YACtB,OAAmB;AACL,sBAAQ,CAAC,QAAQ;AACjB,sBAAQ;AAAA,YACT;AAAA,UACb,WAAqB,UAAU,MAAM;AACzB,oBAAQ;AACR,gBAAI,UAAU;AACZ,sBAAQ,CAAC,QAAQ;AAAA,YAC/B,OAAmB;AACL,sBAAQ,CAAC,QAAQ;AAAA,YAClB;AAAA,UACF;AACD,cAAI,UAAU,KAAK;AACjB,0BAAc;AAAA,UACf;AACD,iBAAO,GAAG,QAAQ,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,WAAW;AAAA,QACxD,WAAU,UAAU;AACnB,iBAAO,KAAK,KAAK,OAAO,WAAW,KAAK,CAAC,QAAQ,CAAC;AAAA,QACnD,WAAU,UAAU;AACnB,iBAAO,KAAK,KAAK,IAAI,KAAK,KAAK,WAAW,KAAK,KAAK,IAAI,CAAC,QAAQ,CAAC;AAAA,QACnE;AACD,eAAO;AAAA,MACR;AAAA,IACP;AAAA,EACA,CAAG,EAAE,KAAK,GAAG;AACb;AACA,SAAS,UAAU,OAAO;AACxB,SAAO,MAAM,OAAO,QAAQ,WAAW,IAAI,GAAG,EAAE;AAClD;AACA,SAAS,UAAU,kBAAkB;AACnC,SAAO,iBAAiB,OAAO,QAAQ,WAAW,IAAI,GAAG,EAAE;AAC7D;AACA,SAAS,YAAY,WAAW,aAAa;AAC3C,cAAY,CAAC,aAAa;AAC1B,gBAAc,CAAC,eAAe;AAC9B,MAAI,YAAY,aAAa;AAC3B,WAAO;AAAA,EACR;AACD,MAAI,cAAc,aAAa;AAC7B,WAAO;AAAA,EACR;AACD,SAAO;AACT;AACA,SAAS,kBAAkB,WAAW,aAAa;AACjD,QAAM,EAAE,YAAY,gBAAiB,IAAG;AACxC,QAAM,EAAE,YAAY,kBAAmB,IAAG;AAC1C,MAAI,oBAAoB,UAAU,CAAC,CAAC,mBAAmB;AACrD,WAAO;AAAA,EACR;AACD,MAAI,CAAC,CAAC,mBAAmB,sBAAsB,QAAQ;AACrD,WAAO;AAAA,EACR;AACD,MAAI,oBAAoB,UAAU,sBAAsB,QAAQ;AAC9D,WAAO;AAAA,EACR;AACD,WAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK,GAAG,KAAK;AACvD,UAAM,eAAe,gBAAgB,CAAC;AACtC,UAAM,iBAAiB,kBAAkB,CAAC;AAC1C,QAAI,iBAAiB,gBAAgB;AACnC;AAAA,IACD;AACD,QAAI,iBAAiB,UAAU,mBAAmB,QAAQ;AACxD,aAAO;AAAA,IACR;AACD,QAAI,CAAC,cAAc;AACjB,aAAO;AAAA,IACR;AACD,QAAI,CAAC,gBAAgB;AACnB,aAAO;AAAA,IACR;AACD,WAAO,YAAY,cAAc,cAAc;AAAA,EAChD;AACD,SAAO;AACT;AACA,SAAS,eAAe,WAAW,aAAa;AAC9C,SAAO,YAAY,UAAU,OAAO,YAAY,KAAK,KAAK,YAAY,UAAU,OAAO,YAAY,KAAK,KAAK,YAAY,UAAU,OAAO,YAAY,KAAK,KAAK,kBAAkB,WAAW,WAAW;AAC1M;AACA,SAAS,GAAG,WAAW,aAAa;AAClC,SAAO,UAAU,YAAY,YAAY;AAC3C;AACA,SAAS,QAAQ,WAAW,aAAa;AACvC,UAAQ,UAAU,UAAQ;AAAA,IACxB,KAAK;AAAA,IACL,KAAK;AACH,aAAO,GAAG,WAAW,WAAW;AAAA,IAClC,KAAK;AACH,aAAO,eAAe,WAAW,WAAW,IAAI;AAAA,IAClD,KAAK;AACH,aAAO,GAAG,WAAW,WAAW,KAAK,eAAe,WAAW,WAAW,IAAI;AAAA,IAChF,KAAK;AACH,aAAO,eAAe,WAAW,WAAW,IAAI;AAAA,IAClD,KAAK;AACH,aAAO,GAAG,WAAW,WAAW,KAAK,eAAe,WAAW,WAAW,IAAI;AAAA,IAChF,KAAK,QAAQ;AACX,aAAO;AAAA,IACR;AAAA,IACD;AACE,aAAO;AAAA,EACV;AACH;AACA,SAAS,sBAAsB,OAAO;AACpC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD,EAAC,KAAK;AACT;AACA,SAAS,WAAW,OAAO;AACzB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,EAAI,MAAM,KAAI,CAAE,EAAE,MAAM,KAAK,EAAE,KAAK,GAAG;AACvC;AACA,SAAS,QAAQ,SAAS,OAAO;AAC/B,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACR;AACD,QAAM,cAAc,WAAW,KAAK;AACpC,QAAM,mBAAmB,YAAY,MAAM,GAAG,EAAE,IAAI,CAAC,iBAAiB,sBAAsB,YAAY,CAAC,EAAE,KAAK,GAAG;AACnH,QAAM,cAAc,iBAAiB,MAAM,KAAK,EAAE,IAAI,CAAC,gBAAgB,UAAU,WAAW,CAAC;AAC7F,QAAM,mBAAmB,kBAAkB,OAAO;AAClD,MAAI,CAAC,kBAAkB;AACrB,WAAO;AAAA,EACR;AACD,QAAM;AAAA,IACR;AAAA,IACI;AAAA,IACJ;AAAA,IACI;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD,IAAG;AACJ,QAAM,cAAc;AAAA,IAClB,UAAU;AAAA,IACV,SAAS;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,IACD,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,YAAY,qBAAqB,OAAO,SAAS,kBAAkB,MAAM,GAAG;AAAA,EAChF;AACE,aAAW,eAAe,aAAa;AACrC,UAAM,sBAAsB,kBAAkB,WAAW;AACzD,QAAI,CAAC,qBAAqB;AACxB,aAAO;AAAA,IACR;AACD,UAAM;AAAA,MACV;AAAA,MACM;AAAA,MACN;AAAA,MACM;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD,IAAG;AACJ,UAAM,YAAY;AAAA,MAChB,UAAU;AAAA,MACV,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAY,mBAAmB,OAAO,SAAS,gBAAgB,MAAM,GAAG;AAAA,IAC9E;AACI,QAAI,CAAC,QAAQ,WAAW,WAAW,GAAG;AACpC,aAAO;AAAA,IACR;AAAA,EACF;AACD,SAAO;AACT", "names": [], "sources": ["../../node_modules/@originjs/vite-plugin-federation/dist/satisfy.mjs"], "sourcesContent": ["const buildIdentifier = \"[0-9A-Za-z-]+\";\nconst build = `(?:\\\\+(${buildIdentifier}(?:\\\\.${buildIdentifier})*))`;\nconst numericIdentifier = \"0|[1-9]\\\\d*\";\nconst numericIdentifierLoose = \"[0-9]+\";\nconst nonNumericIdentifier = \"\\\\d*[a-zA-Z-][a-zA-Z0-9-]*\";\nconst preReleaseIdentifierLoose = `(?:${numericIdentifierLoose}|${nonNumericIdentifier})`;\nconst preReleaseLoose = `(?:-?(${preReleaseIdentifierLoose}(?:\\\\.${preReleaseIdentifierLoose})*))`;\nconst preReleaseIdentifier = `(?:${numericIdentifier}|${nonNumericIdentifier})`;\nconst preRelease = `(?:-(${preReleaseIdentifier}(?:\\\\.${preReleaseIdentifier})*))`;\nconst xRangeIdentifier = `${numericIdentifier}|x|X|\\\\*`;\nconst xRangePlain = `[v=\\\\s]*(${xRangeIdentifier})(?:\\\\.(${xRangeIdentifier})(?:\\\\.(${xRangeIdentifier})(?:${preRelease})?${build}?)?)?`;\nconst hyphenRange = `^\\\\s*(${xRangePlain})\\\\s+-\\\\s+(${xRangePlain})\\\\s*$`;\nconst mainVersionLoose = `(${numericIdentifierLoose})\\\\.(${numericIdentifierLoose})\\\\.(${numericIdentifierLoose})`;\nconst loosePlain = `[v=\\\\s]*${mainVersionLoose}${preReleaseLoose}?${build}?`;\nconst gtlt = \"((?:<|>)?=?)\";\nconst comparatorTrim = `(\\\\s*)${gtlt}\\\\s*(${loosePlain}|${xRangePlain})`;\nconst loneTilde = \"(?:~>?)\";\nconst tildeTrim = `(\\\\s*)${loneTilde}\\\\s+`;\nconst loneCaret = \"(?:\\\\^)\";\nconst caretTrim = `(\\\\s*)${loneCaret}\\\\s+`;\nconst star = \"(<|>)?=?\\\\s*\\\\*\";\nconst caret = `^${loneCaret}${xRangePlain}$`;\nconst mainVersion = `(${numericIdentifier})\\\\.(${numericIdentifier})\\\\.(${numericIdentifier})`;\nconst fullPlain = `v?${mainVersion}${preRelease}?${build}?`;\nconst tilde = `^${loneTilde}${xRangePlain}$`;\nconst xRange = `^${gtlt}\\\\s*${xRangePlain}$`;\nconst comparator = `^${gtlt}\\\\s*(${fullPlain})$|^$`;\nconst gte0 = \"^\\\\s*>=\\\\s*0.0.0\\\\s*$\";\nfunction parseRegex(source) {\n  return new RegExp(source);\n}\nfunction isXVersion(version) {\n  return !version || version.toLowerCase() === \"x\" || version === \"*\";\n}\nfunction pipe(...fns) {\n  return (x) => {\n    return fns.reduce((v, f) => f(v), x);\n  };\n}\nfunction extractComparator(comparatorString) {\n  return comparatorString.match(parseRegex(comparator));\n}\nfunction combineVersion(major, minor, patch, preRelease2) {\n  const mainVersion2 = `${major}.${minor}.${patch}`;\n  if (preRelease2) {\n    return `${mainVersion2}-${preRelease2}`;\n  }\n  return mainVersion2;\n}\nfunction parseHyphen(range) {\n  return range.replace(\n    parseRegex(hyphenRange),\n    (_range, from, fromMajor, fromMinor, fromPatch, _fromPreRelease, _fromBuild, to, toMajor, toMinor, toPatch, toPreRelease) => {\n      if (isXVersion(fromMajor)) {\n        from = \"\";\n      } else if (isXVersion(fromMinor)) {\n        from = `>=${fromMajor}.0.0`;\n      } else if (isXVersion(fromPatch)) {\n        from = `>=${fromMajor}.${fromMinor}.0`;\n      } else {\n        from = `>=${from}`;\n      }\n      if (isXVersion(toMajor)) {\n        to = \"\";\n      } else if (isXVersion(toMinor)) {\n        to = `<${+toMajor + 1}.0.0-0`;\n      } else if (isXVersion(toPatch)) {\n        to = `<${toMajor}.${+toMinor + 1}.0-0`;\n      } else if (toPreRelease) {\n        to = `<=${toMajor}.${toMinor}.${toPatch}-${toPreRelease}`;\n      } else {\n        to = `<=${to}`;\n      }\n      return `${from} ${to}`.trim();\n    }\n  );\n}\nfunction parseComparatorTrim(range) {\n  return range.replace(parseRegex(comparatorTrim), \"$1$2$3\");\n}\nfunction parseTildeTrim(range) {\n  return range.replace(parseRegex(tildeTrim), \"$1~\");\n}\nfunction parseCaretTrim(range) {\n  return range.replace(parseRegex(caretTrim), \"$1^\");\n}\nfunction parseCarets(range) {\n  return range.trim().split(/\\s+/).map((rangeVersion) => {\n    return rangeVersion.replace(\n      parseRegex(caret),\n      (_, major, minor, patch, preRelease2) => {\n        if (isXVersion(major)) {\n          return \"\";\n        } else if (isXVersion(minor)) {\n          return `>=${major}.0.0 <${+major + 1}.0.0-0`;\n        } else if (isXVersion(patch)) {\n          if (major === \"0\") {\n            return `>=${major}.${minor}.0 <${major}.${+minor + 1}.0-0`;\n          } else {\n            return `>=${major}.${minor}.0 <${+major + 1}.0.0-0`;\n          }\n        } else if (preRelease2) {\n          if (major === \"0\") {\n            if (minor === \"0\") {\n              return `>=${major}.${minor}.${patch}-${preRelease2} <${major}.${minor}.${+patch + 1}-0`;\n            } else {\n              return `>=${major}.${minor}.${patch}-${preRelease2} <${major}.${+minor + 1}.0-0`;\n            }\n          } else {\n            return `>=${major}.${minor}.${patch}-${preRelease2} <${+major + 1}.0.0-0`;\n          }\n        } else {\n          if (major === \"0\") {\n            if (minor === \"0\") {\n              return `>=${major}.${minor}.${patch} <${major}.${minor}.${+patch + 1}-0`;\n            } else {\n              return `>=${major}.${minor}.${patch} <${major}.${+minor + 1}.0-0`;\n            }\n          }\n          return `>=${major}.${minor}.${patch} <${+major + 1}.0.0-0`;\n        }\n      }\n    );\n  }).join(\" \");\n}\nfunction parseTildes(range) {\n  return range.trim().split(/\\s+/).map((rangeVersion) => {\n    return rangeVersion.replace(\n      parseRegex(tilde),\n      (_, major, minor, patch, preRelease2) => {\n        if (isXVersion(major)) {\n          return \"\";\n        } else if (isXVersion(minor)) {\n          return `>=${major}.0.0 <${+major + 1}.0.0-0`;\n        } else if (isXVersion(patch)) {\n          return `>=${major}.${minor}.0 <${major}.${+minor + 1}.0-0`;\n        } else if (preRelease2) {\n          return `>=${major}.${minor}.${patch}-${preRelease2} <${major}.${+minor + 1}.0-0`;\n        }\n        return `>=${major}.${minor}.${patch} <${major}.${+minor + 1}.0-0`;\n      }\n    );\n  }).join(\" \");\n}\nfunction parseXRanges(range) {\n  return range.split(/\\s+/).map((rangeVersion) => {\n    return rangeVersion.trim().replace(\n      parseRegex(xRange),\n      (ret, gtlt2, major, minor, patch, preRelease2) => {\n        const isXMajor = isXVersion(major);\n        const isXMinor = isXMajor || isXVersion(minor);\n        const isXPatch = isXMinor || isXVersion(patch);\n        if (gtlt2 === \"=\" && isXPatch) {\n          gtlt2 = \"\";\n        }\n        preRelease2 = \"\";\n        if (isXMajor) {\n          if (gtlt2 === \">\" || gtlt2 === \"<\") {\n            return \"<0.0.0-0\";\n          } else {\n            return \"*\";\n          }\n        } else if (gtlt2 && isXPatch) {\n          if (isXMinor) {\n            minor = 0;\n          }\n          patch = 0;\n          if (gtlt2 === \">\") {\n            gtlt2 = \">=\";\n            if (isXMinor) {\n              major = +major + 1;\n              minor = 0;\n              patch = 0;\n            } else {\n              minor = +minor + 1;\n              patch = 0;\n            }\n          } else if (gtlt2 === \"<=\") {\n            gtlt2 = \"<\";\n            if (isXMinor) {\n              major = +major + 1;\n            } else {\n              minor = +minor + 1;\n            }\n          }\n          if (gtlt2 === \"<\") {\n            preRelease2 = \"-0\";\n          }\n          return `${gtlt2 + major}.${minor}.${patch}${preRelease2}`;\n        } else if (isXMinor) {\n          return `>=${major}.0.0${preRelease2} <${+major + 1}.0.0-0`;\n        } else if (isXPatch) {\n          return `>=${major}.${minor}.0${preRelease2} <${major}.${+minor + 1}.0-0`;\n        }\n        return ret;\n      }\n    );\n  }).join(\" \");\n}\nfunction parseStar(range) {\n  return range.trim().replace(parseRegex(star), \"\");\n}\nfunction parseGTE0(comparatorString) {\n  return comparatorString.trim().replace(parseRegex(gte0), \"\");\n}\nfunction compareAtom(rangeAtom, versionAtom) {\n  rangeAtom = +rangeAtom || rangeAtom;\n  versionAtom = +versionAtom || versionAtom;\n  if (rangeAtom > versionAtom) {\n    return 1;\n  }\n  if (rangeAtom === versionAtom) {\n    return 0;\n  }\n  return -1;\n}\nfunction comparePreRelease(rangeAtom, versionAtom) {\n  const { preRelease: rangePreRelease } = rangeAtom;\n  const { preRelease: versionPreRelease } = versionAtom;\n  if (rangePreRelease === void 0 && !!versionPreRelease) {\n    return 1;\n  }\n  if (!!rangePreRelease && versionPreRelease === void 0) {\n    return -1;\n  }\n  if (rangePreRelease === void 0 && versionPreRelease === void 0) {\n    return 0;\n  }\n  for (let i = 0, n = rangePreRelease.length; i <= n; i++) {\n    const rangeElement = rangePreRelease[i];\n    const versionElement = versionPreRelease[i];\n    if (rangeElement === versionElement) {\n      continue;\n    }\n    if (rangeElement === void 0 && versionElement === void 0) {\n      return 0;\n    }\n    if (!rangeElement) {\n      return 1;\n    }\n    if (!versionElement) {\n      return -1;\n    }\n    return compareAtom(rangeElement, versionElement);\n  }\n  return 0;\n}\nfunction compareVersion(rangeAtom, versionAtom) {\n  return compareAtom(rangeAtom.major, versionAtom.major) || compareAtom(rangeAtom.minor, versionAtom.minor) || compareAtom(rangeAtom.patch, versionAtom.patch) || comparePreRelease(rangeAtom, versionAtom);\n}\nfunction eq(rangeAtom, versionAtom) {\n  return rangeAtom.version === versionAtom.version;\n}\nfunction compare(rangeAtom, versionAtom) {\n  switch (rangeAtom.operator) {\n    case \"\":\n    case \"=\":\n      return eq(rangeAtom, versionAtom);\n    case \">\":\n      return compareVersion(rangeAtom, versionAtom) < 0;\n    case \">=\":\n      return eq(rangeAtom, versionAtom) || compareVersion(rangeAtom, versionAtom) < 0;\n    case \"<\":\n      return compareVersion(rangeAtom, versionAtom) > 0;\n    case \"<=\":\n      return eq(rangeAtom, versionAtom) || compareVersion(rangeAtom, versionAtom) > 0;\n    case void 0: {\n      return true;\n    }\n    default:\n      return false;\n  }\n}\nfunction parseComparatorString(range) {\n  return pipe(\n    parseCarets,\n    parseTildes,\n    parseXRanges,\n    parseStar\n  )(range);\n}\nfunction parseRange(range) {\n  return pipe(\n    parseHyphen,\n    parseComparatorTrim,\n    parseTildeTrim,\n    parseCaretTrim\n  )(range.trim()).split(/\\s+/).join(\" \");\n}\nfunction satisfy(version, range) {\n  if (!version) {\n    return false;\n  }\n  const parsedRange = parseRange(range);\n  const parsedComparator = parsedRange.split(\" \").map((rangeVersion) => parseComparatorString(rangeVersion)).join(\" \");\n  const comparators = parsedComparator.split(/\\s+/).map((comparator2) => parseGTE0(comparator2));\n  const extractedVersion = extractComparator(version);\n  if (!extractedVersion) {\n    return false;\n  }\n  const [\n    ,\n    versionOperator,\n    ,\n    versionMajor,\n    versionMinor,\n    versionPatch,\n    versionPreRelease\n  ] = extractedVersion;\n  const versionAtom = {\n    operator: versionOperator,\n    version: combineVersion(\n      versionMajor,\n      versionMinor,\n      versionPatch,\n      versionPreRelease\n    ),\n    major: versionMajor,\n    minor: versionMinor,\n    patch: versionPatch,\n    preRelease: versionPreRelease == null ? void 0 : versionPreRelease.split(\".\")\n  };\n  for (const comparator2 of comparators) {\n    const extractedComparator = extractComparator(comparator2);\n    if (!extractedComparator) {\n      return false;\n    }\n    const [\n      ,\n      rangeOperator,\n      ,\n      rangeMajor,\n      rangeMinor,\n      rangePatch,\n      rangePreRelease\n    ] = extractedComparator;\n    const rangeAtom = {\n      operator: rangeOperator,\n      version: combineVersion(\n        rangeMajor,\n        rangeMinor,\n        rangePatch,\n        rangePreRelease\n      ),\n      major: rangeMajor,\n      minor: rangeMinor,\n      patch: rangePatch,\n      preRelease: rangePreRelease == null ? void 0 : rangePreRelease.split(\".\")\n    };\n    if (!compare(rangeAtom, versionAtom)) {\n      return false;\n    }\n  }\n  return true;\n}\nexport {\n  satisfy\n};\n"], "file": "assets/__federation_fn_import-CVKrYLve.js"}