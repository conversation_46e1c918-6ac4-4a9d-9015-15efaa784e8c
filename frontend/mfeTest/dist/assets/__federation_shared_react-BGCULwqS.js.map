{"version": 3, "file": "__federation_shared_react-BGCULwqS.js", "sources": ["../../node_modules/react/cjs/react.production.js", "../../node_modules/react/index.js"], "sourcesContent": ["/**\n * @license React\n * react.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n  REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n  REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n  REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n  REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n  REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n  REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n  REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n  REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n  REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n  REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n  MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nfunction getIteratorFn(maybeIterable) {\n  if (null === maybeIterable || \"object\" !== typeof maybeIterable) return null;\n  maybeIterable =\n    (MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL]) ||\n    maybeIterable[\"@@iterator\"];\n  return \"function\" === typeof maybeIterable ? maybeIterable : null;\n}\nvar ReactNoopUpdateQueue = {\n    isMounted: function () {\n      return !1;\n    },\n    enqueueForceUpdate: function () {},\n    enqueueReplaceState: function () {},\n    enqueueSetState: function () {}\n  },\n  assign = Object.assign,\n  emptyObject = {};\nfunction Component(props, context, updater) {\n  this.props = props;\n  this.context = context;\n  this.refs = emptyObject;\n  this.updater = updater || ReactNoopUpdateQueue;\n}\nComponent.prototype.isReactComponent = {};\nComponent.prototype.setState = function (partialState, callback) {\n  if (\n    \"object\" !== typeof partialState &&\n    \"function\" !== typeof partialState &&\n    null != partialState\n  )\n    throw Error(\n      \"takes an object of state variables to update or a function which returns an object of state variables.\"\n    );\n  this.updater.enqueueSetState(this, partialState, callback, \"setState\");\n};\nComponent.prototype.forceUpdate = function (callback) {\n  this.updater.enqueueForceUpdate(this, callback, \"forceUpdate\");\n};\nfunction ComponentDummy() {}\nComponentDummy.prototype = Component.prototype;\nfunction PureComponent(props, context, updater) {\n  this.props = props;\n  this.context = context;\n  this.refs = emptyObject;\n  this.updater = updater || ReactNoopUpdateQueue;\n}\nvar pureComponentPrototype = (PureComponent.prototype = new ComponentDummy());\npureComponentPrototype.constructor = PureComponent;\nassign(pureComponentPrototype, Component.prototype);\npureComponentPrototype.isPureReactComponent = !0;\nvar isArrayImpl = Array.isArray,\n  ReactSharedInternals = { H: null, A: null, T: null, S: null, V: null },\n  hasOwnProperty = Object.prototype.hasOwnProperty;\nfunction ReactElement(type, key, self, source, owner, props) {\n  self = props.ref;\n  return {\n    $$typeof: REACT_ELEMENT_TYPE,\n    type: type,\n    key: key,\n    ref: void 0 !== self ? self : null,\n    props: props\n  };\n}\nfunction cloneAndReplaceKey(oldElement, newKey) {\n  return ReactElement(\n    oldElement.type,\n    newKey,\n    void 0,\n    void 0,\n    void 0,\n    oldElement.props\n  );\n}\nfunction isValidElement(object) {\n  return (\n    \"object\" === typeof object &&\n    null !== object &&\n    object.$$typeof === REACT_ELEMENT_TYPE\n  );\n}\nfunction escape(key) {\n  var escaperLookup = { \"=\": \"=0\", \":\": \"=2\" };\n  return (\n    \"$\" +\n    key.replace(/[=:]/g, function (match) {\n      return escaperLookup[match];\n    })\n  );\n}\nvar userProvidedKeyEscapeRegex = /\\/+/g;\nfunction getElementKey(element, index) {\n  return \"object\" === typeof element && null !== element && null != element.key\n    ? escape(\"\" + element.key)\n    : index.toString(36);\n}\nfunction noop$1() {}\nfunction resolveThenable(thenable) {\n  switch (thenable.status) {\n    case \"fulfilled\":\n      return thenable.value;\n    case \"rejected\":\n      throw thenable.reason;\n    default:\n      switch (\n        (\"string\" === typeof thenable.status\n          ? thenable.then(noop$1, noop$1)\n          : ((thenable.status = \"pending\"),\n            thenable.then(\n              function (fulfilledValue) {\n                \"pending\" === thenable.status &&\n                  ((thenable.status = \"fulfilled\"),\n                  (thenable.value = fulfilledValue));\n              },\n              function (error) {\n                \"pending\" === thenable.status &&\n                  ((thenable.status = \"rejected\"), (thenable.reason = error));\n              }\n            )),\n        thenable.status)\n      ) {\n        case \"fulfilled\":\n          return thenable.value;\n        case \"rejected\":\n          throw thenable.reason;\n      }\n  }\n  throw thenable;\n}\nfunction mapIntoArray(children, array, escapedPrefix, nameSoFar, callback) {\n  var type = typeof children;\n  if (\"undefined\" === type || \"boolean\" === type) children = null;\n  var invokeCallback = !1;\n  if (null === children) invokeCallback = !0;\n  else\n    switch (type) {\n      case \"bigint\":\n      case \"string\":\n      case \"number\":\n        invokeCallback = !0;\n        break;\n      case \"object\":\n        switch (children.$$typeof) {\n          case REACT_ELEMENT_TYPE:\n          case REACT_PORTAL_TYPE:\n            invokeCallback = !0;\n            break;\n          case REACT_LAZY_TYPE:\n            return (\n              (invokeCallback = children._init),\n              mapIntoArray(\n                invokeCallback(children._payload),\n                array,\n                escapedPrefix,\n                nameSoFar,\n                callback\n              )\n            );\n        }\n    }\n  if (invokeCallback)\n    return (\n      (callback = callback(children)),\n      (invokeCallback =\n        \"\" === nameSoFar ? \".\" + getElementKey(children, 0) : nameSoFar),\n      isArrayImpl(callback)\n        ? ((escapedPrefix = \"\"),\n          null != invokeCallback &&\n            (escapedPrefix =\n              invokeCallback.replace(userProvidedKeyEscapeRegex, \"$&/\") + \"/\"),\n          mapIntoArray(callback, array, escapedPrefix, \"\", function (c) {\n            return c;\n          }))\n        : null != callback &&\n          (isValidElement(callback) &&\n            (callback = cloneAndReplaceKey(\n              callback,\n              escapedPrefix +\n                (null == callback.key ||\n                (children && children.key === callback.key)\n                  ? \"\"\n                  : (\"\" + callback.key).replace(\n                      userProvidedKeyEscapeRegex,\n                      \"$&/\"\n                    ) + \"/\") +\n                invokeCallback\n            )),\n          array.push(callback)),\n      1\n    );\n  invokeCallback = 0;\n  var nextNamePrefix = \"\" === nameSoFar ? \".\" : nameSoFar + \":\";\n  if (isArrayImpl(children))\n    for (var i = 0; i < children.length; i++)\n      (nameSoFar = children[i]),\n        (type = nextNamePrefix + getElementKey(nameSoFar, i)),\n        (invokeCallback += mapIntoArray(\n          nameSoFar,\n          array,\n          escapedPrefix,\n          type,\n          callback\n        ));\n  else if (((i = getIteratorFn(children)), \"function\" === typeof i))\n    for (\n      children = i.call(children), i = 0;\n      !(nameSoFar = children.next()).done;\n\n    )\n      (nameSoFar = nameSoFar.value),\n        (type = nextNamePrefix + getElementKey(nameSoFar, i++)),\n        (invokeCallback += mapIntoArray(\n          nameSoFar,\n          array,\n          escapedPrefix,\n          type,\n          callback\n        ));\n  else if (\"object\" === type) {\n    if (\"function\" === typeof children.then)\n      return mapIntoArray(\n        resolveThenable(children),\n        array,\n        escapedPrefix,\n        nameSoFar,\n        callback\n      );\n    array = String(children);\n    throw Error(\n      \"Objects are not valid as a React child (found: \" +\n        (\"[object Object]\" === array\n          ? \"object with keys {\" + Object.keys(children).join(\", \") + \"}\"\n          : array) +\n        \"). If you meant to render a collection of children, use an array instead.\"\n    );\n  }\n  return invokeCallback;\n}\nfunction mapChildren(children, func, context) {\n  if (null == children) return children;\n  var result = [],\n    count = 0;\n  mapIntoArray(children, result, \"\", \"\", function (child) {\n    return func.call(context, child, count++);\n  });\n  return result;\n}\nfunction lazyInitializer(payload) {\n  if (-1 === payload._status) {\n    var ctor = payload._result;\n    ctor = ctor();\n    ctor.then(\n      function (moduleObject) {\n        if (0 === payload._status || -1 === payload._status)\n          (payload._status = 1), (payload._result = moduleObject);\n      },\n      function (error) {\n        if (0 === payload._status || -1 === payload._status)\n          (payload._status = 2), (payload._result = error);\n      }\n    );\n    -1 === payload._status && ((payload._status = 0), (payload._result = ctor));\n  }\n  if (1 === payload._status) return payload._result.default;\n  throw payload._result;\n}\nvar reportGlobalError =\n  \"function\" === typeof reportError\n    ? reportError\n    : function (error) {\n        if (\n          \"object\" === typeof window &&\n          \"function\" === typeof window.ErrorEvent\n        ) {\n          var event = new window.ErrorEvent(\"error\", {\n            bubbles: !0,\n            cancelable: !0,\n            message:\n              \"object\" === typeof error &&\n              null !== error &&\n              \"string\" === typeof error.message\n                ? String(error.message)\n                : String(error),\n            error: error\n          });\n          if (!window.dispatchEvent(event)) return;\n        } else if (\n          \"object\" === typeof process &&\n          \"function\" === typeof process.emit\n        ) {\n          process.emit(\"uncaughtException\", error);\n          return;\n        }\n        console.error(error);\n      };\nfunction noop() {}\nexports.Children = {\n  map: mapChildren,\n  forEach: function (children, forEachFunc, forEachContext) {\n    mapChildren(\n      children,\n      function () {\n        forEachFunc.apply(this, arguments);\n      },\n      forEachContext\n    );\n  },\n  count: function (children) {\n    var n = 0;\n    mapChildren(children, function () {\n      n++;\n    });\n    return n;\n  },\n  toArray: function (children) {\n    return (\n      mapChildren(children, function (child) {\n        return child;\n      }) || []\n    );\n  },\n  only: function (children) {\n    if (!isValidElement(children))\n      throw Error(\n        \"React.Children.only expected to receive a single React element child.\"\n      );\n    return children;\n  }\n};\nexports.Component = Component;\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.Profiler = REACT_PROFILER_TYPE;\nexports.PureComponent = PureComponent;\nexports.StrictMode = REACT_STRICT_MODE_TYPE;\nexports.Suspense = REACT_SUSPENSE_TYPE;\nexports.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE =\n  ReactSharedInternals;\nexports.__COMPILER_RUNTIME = {\n  __proto__: null,\n  c: function (size) {\n    return ReactSharedInternals.H.useMemoCache(size);\n  }\n};\nexports.cache = function (fn) {\n  return function () {\n    return fn.apply(null, arguments);\n  };\n};\nexports.cloneElement = function (element, config, children) {\n  if (null === element || void 0 === element)\n    throw Error(\n      \"The argument must be a React element, but you passed \" + element + \".\"\n    );\n  var props = assign({}, element.props),\n    key = element.key,\n    owner = void 0;\n  if (null != config)\n    for (propName in (void 0 !== config.ref && (owner = void 0),\n    void 0 !== config.key && (key = \"\" + config.key),\n    config))\n      !hasOwnProperty.call(config, propName) ||\n        \"key\" === propName ||\n        \"__self\" === propName ||\n        \"__source\" === propName ||\n        (\"ref\" === propName && void 0 === config.ref) ||\n        (props[propName] = config[propName]);\n  var propName = arguments.length - 2;\n  if (1 === propName) props.children = children;\n  else if (1 < propName) {\n    for (var childArray = Array(propName), i = 0; i < propName; i++)\n      childArray[i] = arguments[i + 2];\n    props.children = childArray;\n  }\n  return ReactElement(element.type, key, void 0, void 0, owner, props);\n};\nexports.createContext = function (defaultValue) {\n  defaultValue = {\n    $$typeof: REACT_CONTEXT_TYPE,\n    _currentValue: defaultValue,\n    _currentValue2: defaultValue,\n    _threadCount: 0,\n    Provider: null,\n    Consumer: null\n  };\n  defaultValue.Provider = defaultValue;\n  defaultValue.Consumer = {\n    $$typeof: REACT_CONSUMER_TYPE,\n    _context: defaultValue\n  };\n  return defaultValue;\n};\nexports.createElement = function (type, config, children) {\n  var propName,\n    props = {},\n    key = null;\n  if (null != config)\n    for (propName in (void 0 !== config.key && (key = \"\" + config.key), config))\n      hasOwnProperty.call(config, propName) &&\n        \"key\" !== propName &&\n        \"__self\" !== propName &&\n        \"__source\" !== propName &&\n        (props[propName] = config[propName]);\n  var childrenLength = arguments.length - 2;\n  if (1 === childrenLength) props.children = children;\n  else if (1 < childrenLength) {\n    for (var childArray = Array(childrenLength), i = 0; i < childrenLength; i++)\n      childArray[i] = arguments[i + 2];\n    props.children = childArray;\n  }\n  if (type && type.defaultProps)\n    for (propName in ((childrenLength = type.defaultProps), childrenLength))\n      void 0 === props[propName] &&\n        (props[propName] = childrenLength[propName]);\n  return ReactElement(type, key, void 0, void 0, null, props);\n};\nexports.createRef = function () {\n  return { current: null };\n};\nexports.forwardRef = function (render) {\n  return { $$typeof: REACT_FORWARD_REF_TYPE, render: render };\n};\nexports.isValidElement = isValidElement;\nexports.lazy = function (ctor) {\n  return {\n    $$typeof: REACT_LAZY_TYPE,\n    _payload: { _status: -1, _result: ctor },\n    _init: lazyInitializer\n  };\n};\nexports.memo = function (type, compare) {\n  return {\n    $$typeof: REACT_MEMO_TYPE,\n    type: type,\n    compare: void 0 === compare ? null : compare\n  };\n};\nexports.startTransition = function (scope) {\n  var prevTransition = ReactSharedInternals.T,\n    currentTransition = {};\n  ReactSharedInternals.T = currentTransition;\n  try {\n    var returnValue = scope(),\n      onStartTransitionFinish = ReactSharedInternals.S;\n    null !== onStartTransitionFinish &&\n      onStartTransitionFinish(currentTransition, returnValue);\n    \"object\" === typeof returnValue &&\n      null !== returnValue &&\n      \"function\" === typeof returnValue.then &&\n      returnValue.then(noop, reportGlobalError);\n  } catch (error) {\n    reportGlobalError(error);\n  } finally {\n    ReactSharedInternals.T = prevTransition;\n  }\n};\nexports.unstable_useCacheRefresh = function () {\n  return ReactSharedInternals.H.useCacheRefresh();\n};\nexports.use = function (usable) {\n  return ReactSharedInternals.H.use(usable);\n};\nexports.useActionState = function (action, initialState, permalink) {\n  return ReactSharedInternals.H.useActionState(action, initialState, permalink);\n};\nexports.useCallback = function (callback, deps) {\n  return ReactSharedInternals.H.useCallback(callback, deps);\n};\nexports.useContext = function (Context) {\n  return ReactSharedInternals.H.useContext(Context);\n};\nexports.useDebugValue = function () {};\nexports.useDeferredValue = function (value, initialValue) {\n  return ReactSharedInternals.H.useDeferredValue(value, initialValue);\n};\nexports.useEffect = function (create, createDeps, update) {\n  var dispatcher = ReactSharedInternals.H;\n  if (\"function\" === typeof update)\n    throw Error(\n      \"useEffect CRUD overload is not enabled in this build of React.\"\n    );\n  return dispatcher.useEffect(create, createDeps);\n};\nexports.useId = function () {\n  return ReactSharedInternals.H.useId();\n};\nexports.useImperativeHandle = function (ref, create, deps) {\n  return ReactSharedInternals.H.useImperativeHandle(ref, create, deps);\n};\nexports.useInsertionEffect = function (create, deps) {\n  return ReactSharedInternals.H.useInsertionEffect(create, deps);\n};\nexports.useLayoutEffect = function (create, deps) {\n  return ReactSharedInternals.H.useLayoutEffect(create, deps);\n};\nexports.useMemo = function (create, deps) {\n  return ReactSharedInternals.H.useMemo(create, deps);\n};\nexports.useOptimistic = function (passthrough, reducer) {\n  return ReactSharedInternals.H.useOptimistic(passthrough, reducer);\n};\nexports.useReducer = function (reducer, initialArg, init) {\n  return ReactSharedInternals.H.useReducer(reducer, initialArg, init);\n};\nexports.useRef = function (initialValue) {\n  return ReactSharedInternals.H.useRef(initialValue);\n};\nexports.useState = function (initialState) {\n  return ReactSharedInternals.H.useState(initialState);\n};\nexports.useSyncExternalStore = function (\n  subscribe,\n  getSnapshot,\n  getServerSnapshot\n) {\n  return ReactSharedInternals.H.useSyncExternalStore(\n    subscribe,\n    getSnapshot,\n    getServerSnapshot\n  );\n};\nexports.useTransition = function () {\n  return ReactSharedInternals.H.useTransition();\n};\nexports.version = \"19.1.0\";\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react.production.js');\n} else {\n  module.exports = require('./cjs/react.development.js');\n}\n"], "names": ["index", "reactModule", "require$$0"], "mappings": ";;;;;;;;;;;;;;AAWA,IAAI,qBAAqB,OAAO,IAAI,4BAA4B,GAC9D,oBAAoB,OAAO,IAAI,cAAc,GAC7C,sBAAsB,OAAO,IAAI,gBAAgB,GACjD,yBAAyB,OAAO,IAAI,mBAAmB,GACvD,sBAAsB,OAAO,IAAI,gBAAgB,GACjD,sBAAsB,OAAO,IAAI,gBAAgB,GACjD,qBAAqB,OAAO,IAAI,eAAe,GAC/C,yBAAyB,OAAO,IAAI,mBAAmB,GACvD,sBAAsB,OAAO,IAAI,gBAAgB,GACjD,kBAAkB,OAAO,IAAI,YAAY,GACzC,kBAAkB,OAAO,IAAI,YAAY,GACzC,wBAAwB,OAAO;AACjC,SAAS,cAAc,eAAe;AACpC,MAAI,SAAS,iBAAiB,aAAa,OAAO;AAAe,WAAO;AACxE,kBACG,yBAAyB,cAAc,qBAAqB,KAC7D,cAAc,YAAY;AAC5B,SAAO,eAAe,OAAO,gBAAgB,gBAAgB;AAC/D;AACA,IAAI,uBAAuB;AAAA,EACvB,WAAW,WAAY;AACrB,WAAO;AAAA,EACR;AAAA,EACD,oBAAoB,WAAY;AAAA,EAAE;AAAA,EAClC,qBAAqB,WAAY;AAAA,EAAE;AAAA,EACnC,iBAAiB,WAAY;AAAA,EAAE;AAChC,GACD,SAAS,OAAO,QAChB,cAAc,CAAA;AAChB,SAAS,UAAU,OAAO,SAAS,SAAS;AAC1C,OAAK,QAAQ;AACb,OAAK,UAAU;AACf,OAAK,OAAO;AACZ,OAAK,UAAU,WAAW;AAC5B;AACA,UAAU,UAAU,mBAAmB;AACvC,UAAU,UAAU,WAAW,SAAU,cAAc,UAAU;AAC/D,MACE,aAAa,OAAO,gBACpB,eAAe,OAAO,gBACtB,QAAQ;AAER,UAAM;AAAA,MACJ;AAAA,IACN;AACE,OAAK,QAAQ,gBAAgB,MAAM,cAAc,UAAU,UAAU;AACvE;AACA,UAAU,UAAU,cAAc,SAAU,UAAU;AACpD,OAAK,QAAQ,mBAAmB,MAAM,UAAU,aAAa;AAC/D;AACA,SAAS,iBAAiB;AAAE;AAC5B,eAAe,YAAY,UAAU;AACrC,SAAS,cAAc,OAAO,SAAS,SAAS;AAC9C,OAAK,QAAQ;AACb,OAAK,UAAU;AACf,OAAK,OAAO;AACZ,OAAK,UAAU,WAAW;AAC5B;AACA,IAAI,yBAA0B,cAAc,YAAY,IAAI,eAAgB;AAC5E,uBAAuB,cAAc;AACrC,OAAO,wBAAwB,UAAU,SAAS;AAClD,uBAAuB,uBAAuB;AAC9C,IAAI,cAAc,MAAM,SACtB,uBAAuB,EAAE,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,KAAM,GACtE,iBAAiB,OAAO,UAAU;AACpC,SAAS,aAAa,MAAM,KAAK,MAAM,QAAQ,OAAO,OAAO;AAC3D,SAAO,MAAM;AACb,SAAO;AAAA,IACL,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA,KAAK,WAAW,OAAO,OAAO;AAAA,IAC9B;AAAA,EACJ;AACA;AACA,SAAS,mBAAmB,YAAY,QAAQ;AAC9C,SAAO;AAAA,IACL,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW;AAAA,EACf;AACA;AACA,SAAS,eAAe,QAAQ;AAC9B,SACE,aAAa,OAAO,UACpB,SAAS,UACT,OAAO,aAAa;AAExB;AACA,SAAS,OAAO,KAAK;AACnB,MAAI,gBAAgB,EAAE,KAAK,MAAM,KAAK,KAAI;AAC1C,SACE,MACA,IAAI,QAAQ,SAAS,SAAU,OAAO;AACpC,WAAO,cAAc,KAAK;AAAA,EAChC,CAAK;AAEL;AACA,IAAI,6BAA6B;AACjC,SAAS,cAAc,SAASA,QAAO;AACrC,SAAO,aAAa,OAAO,WAAW,SAAS,WAAW,QAAQ,QAAQ,MACtE,OAAO,KAAK,QAAQ,GAAG,IACvBA,OAAM,SAAS,EAAE;AACvB;AACA,SAAS,SAAS;AAAE;AACpB,SAAS,gBAAgB,UAAU;AACjC,UAAQ,SAAS,QAAM;AAAA,IACrB,KAAK;AACH,aAAO,SAAS;AAAA,IAClB,KAAK;AACH,YAAM,SAAS;AAAA,IACjB;AACE,cACG,aAAa,OAAO,SAAS,SAC1B,SAAS,KAAK,QAAQ,MAAM,KAC1B,SAAS,SAAS,WACpB,SAAS;AAAA,QACP,SAAU,gBAAgB;AACxB,wBAAc,SAAS,WACnB,SAAS,SAAS,aACnB,SAAS,QAAQ;AAAA,QACrB;AAAA,QACD,SAAU,OAAO;AACf,wBAAc,SAAS,WACnB,SAAS,SAAS,YAAc,SAAS,SAAS;AAAA,QACvD;AAAA,MACf,IACQ,SAAS,QACjB;AAAA,QACQ,KAAK;AACH,iBAAO,SAAS;AAAA,QAClB,KAAK;AACH,gBAAM,SAAS;AAAA,MAClB;AAAA,EACJ;AACD,QAAM;AACR;AACA,SAAS,aAAa,UAAU,OAAO,eAAe,WAAW,UAAU;AACzE,MAAI,OAAO,OAAO;AAClB,MAAI,gBAAgB,QAAQ,cAAc;AAAM,eAAW;AAC3D,MAAI,iBAAiB;AACrB,MAAI,SAAS;AAAU,qBAAiB;AAAA;AAEtC,YAAQ,MAAI;AAAA,MACV,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,yBAAiB;AACjB;AAAA,MACF,KAAK;AACH,gBAAQ,SAAS,UAAQ;AAAA,UACvB,KAAK;AAAA,UACL,KAAK;AACH,6BAAiB;AACjB;AAAA,UACF,KAAK;AACH,mBACG,iBAAiB,SAAS,OAC3B;AAAA,cACE,eAAe,SAAS,QAAQ;AAAA,cAChC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAAA,QAEN;AAAA,IACJ;AACH,MAAI;AACF,WACG,WAAW,SAAS,QAAQ,GAC5B,iBACC,OAAO,YAAY,MAAM,cAAc,UAAU,CAAC,IAAI,WACxD,YAAY,QAAQ,KACd,gBAAgB,IAClB,QAAQ,mBACL,gBACC,eAAe,QAAQ,4BAA4B,KAAK,IAAI,MAChE,aAAa,UAAU,OAAO,eAAe,IAAI,SAAU,GAAG;AAC5D,aAAO;AAAA,IACnB,CAAW,KACD,QAAQ,aACP,eAAe,QAAQ,MACrB,WAAW;AAAA,MACV;AAAA,MACA,iBACG,QAAQ,SAAS,OACjB,YAAY,SAAS,QAAQ,SAAS,MACnC,MACC,KAAK,SAAS,KAAK;AAAA,QAClB;AAAA,QACA;AAAA,MACD,IAAG,OACR;AAAA,IAChB,IACU,MAAM,KAAK,QAAQ,IACvB;AAEJ,mBAAiB;AACjB,MAAI,iBAAiB,OAAO,YAAY,MAAM,YAAY;AAC1D,MAAI,YAAY,QAAQ;AACtB,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ;AACnC,MAAC,YAAY,SAAS,CAAC,GACpB,OAAO,iBAAiB,cAAc,WAAW,CAAC,GAClD,kBAAkB;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACV;AAAA,WACa,IAAI,cAAc,QAAQ,GAAI,eAAe,OAAO;AAC7D,SACE,WAAW,EAAE,KAAK,QAAQ,GAAG,IAAI,GACjC,EAAE,YAAY,SAAS,KAAM,GAAE;AAG/B,MAAC,YAAY,UAAU,OACpB,OAAO,iBAAiB,cAAc,WAAW,GAAG,GACpD,kBAAkB;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACV;AAAA,WACW,aAAa,MAAM;AAC1B,QAAI,eAAe,OAAO,SAAS;AACjC,aAAO;AAAA,QACL,gBAAgB,QAAQ;AAAA,QACxB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACR;AACI,YAAQ,OAAO,QAAQ;AACvB,UAAM;AAAA,MACJ,qDACG,sBAAsB,QACnB,uBAAuB,OAAO,KAAK,QAAQ,EAAE,KAAK,IAAI,IAAI,MAC1D,SACJ;AAAA,IACR;AAAA,EACG;AACD,SAAO;AACT;AACA,SAAS,YAAY,UAAU,MAAM,SAAS;AAC5C,MAAI,QAAQ;AAAU,WAAO;AAC7B,MAAI,SAAS,CAAE,GACb,QAAQ;AACV,eAAa,UAAU,QAAQ,IAAI,IAAI,SAAU,OAAO;AACtD,WAAO,KAAK,KAAK,SAAS,OAAO,OAAO;AAAA,EAC5C,CAAG;AACD,SAAO;AACT;AACA,SAAS,gBAAgB,SAAS;AAChC,MAAI,OAAO,QAAQ,SAAS;AAC1B,QAAI,OAAO,QAAQ;AACnB,WAAO,KAAI;AACX,SAAK;AAAA,MACH,SAAU,cAAc;AACtB,YAAI,MAAM,QAAQ,WAAW,OAAO,QAAQ;AAC1C,UAAC,QAAQ,UAAU,GAAK,QAAQ,UAAU;AAAA,MAC7C;AAAA,MACD,SAAU,OAAO;AACf,YAAI,MAAM,QAAQ,WAAW,OAAO,QAAQ;AAC1C,UAAC,QAAQ,UAAU,GAAK,QAAQ,UAAU;AAAA,MAC7C;AAAA,IACP;AACI,WAAO,QAAQ,YAAa,QAAQ,UAAU,GAAK,QAAQ,UAAU;AAAA,EACtE;AACD,MAAI,MAAM,QAAQ;AAAS,WAAO,QAAQ,QAAQ;AAClD,QAAM,QAAQ;AAChB;AACA,IAAI,oBACF,eAAe,OAAO,cAClB,cACA,SAAU,OAAO;AACf,MACE,aAAa,OAAO,UACpB,eAAe,OAAO,OAAO,YAC7B;AACA,QAAI,QAAQ,IAAI,OAAO,WAAW,SAAS;AAAA,MACzC,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,SACE,aAAa,OAAO,SACpB,SAAS,SACT,aAAa,OAAO,MAAM,UACtB,OAAO,MAAM,OAAO,IACpB,OAAO,KAAK;AAAA,MAClB;AAAA,IACZ,CAAW;AACD,QAAI,CAAC,OAAO,cAAc,KAAK;AAAG;AAAA,EAC5C,WACU,aAAa,OAAO,WACpB,eAAe,OAAO,QAAQ,MAC9B;AACA,YAAQ,KAAK,qBAAqB,KAAK;AACvC;AAAA,EACD;AACD,UAAQ,MAAM,KAAK;AAC3B;AACA,SAAS,OAAO;AAAE;AAClB,iBAAA,WAAmB;AAAA,EACjB,KAAK;AAAA,EACL,SAAS,SAAU,UAAU,aAAa,gBAAgB;AACxD;AAAA,MACE;AAAA,MACA,WAAY;AACV,oBAAY,MAAM,MAAM,SAAS;AAAA,MAClC;AAAA,MACD;AAAA,IACN;AAAA,EACG;AAAA,EACD,OAAO,SAAU,UAAU;AACzB,QAAI,IAAI;AACR,gBAAY,UAAU,WAAY;AAChC;AAAA,IACN,CAAK;AACD,WAAO;AAAA,EACR;AAAA,EACD,SAAS,SAAU,UAAU;AAC3B,WACE,YAAY,UAAU,SAAU,OAAO;AACrC,aAAO;AAAA,IACR,CAAA,KAAK,CAAE;AAAA,EAEX;AAAA,EACD,MAAM,SAAU,UAAU;AACxB,QAAI,CAAC,eAAe,QAAQ;AAC1B,YAAM;AAAA,QACJ;AAAA,MACR;AACI,WAAO;AAAA,EACR;AACH;AACiB,iBAAA,YAAG;AACJ,iBAAA,WAAG;AACH,iBAAA,WAAG;AACE,iBAAA,gBAAG;AACN,iBAAA,aAAG;AACL,iBAAA,WAAG;AACoD,iBAAA,kEACrE;AACF,iBAAA,qBAA6B;AAAA,EAC3B,WAAW;AAAA,EACX,GAAG,SAAU,MAAM;AACjB,WAAO,qBAAqB,EAAE,aAAa,IAAI;AAAA,EAChD;AACH;AACa,iBAAA,QAAG,SAAU,IAAI;AAC5B,SAAO,WAAY;AACjB,WAAO,GAAG,MAAM,MAAM,SAAS;AAAA,EACnC;AACA;AACA,iBAAA,eAAuB,SAAU,SAAS,QAAQ,UAAU;AAC1D,MAAI,SAAS,WAAW,WAAW;AACjC,UAAM;AAAA,MACJ,0DAA0D,UAAU;AAAA,IAC1E;AACE,MAAI,QAAQ,OAAO,IAAI,QAAQ,KAAK,GAClC,MAAM,QAAQ,KACd,QAAQ;AACV,MAAI,QAAQ;AACV,SAAK,YAAa,WAAW,OAAO,QAAQ,QAAQ,SACpD,WAAW,OAAO,QAAQ,MAAM,KAAK,OAAO,MAC5C;AACE,OAAC,eAAe,KAAK,QAAQ,QAAQ,KACnC,UAAU,YACV,aAAa,YACb,eAAe,YACd,UAAU,YAAY,WAAW,OAAO,QACxC,MAAM,QAAQ,IAAI,OAAO,QAAQ;AACxC,MAAI,WAAW,UAAU,SAAS;AAClC,MAAI,MAAM;AAAU,UAAM,WAAW;AAAA,WAC5B,IAAI,UAAU;AACrB,aAAS,aAAa,MAAM,QAAQ,GAAG,IAAI,GAAG,IAAI,UAAU;AAC1D,iBAAW,CAAC,IAAI,UAAU,IAAI,CAAC;AACjC,UAAM,WAAW;AAAA,EAClB;AACD,SAAO,aAAa,QAAQ,MAAM,KAAK,QAAQ,QAAQ,OAAO,KAAK;AACrE;AACqB,iBAAA,gBAAG,SAAU,cAAc;AAC9C,iBAAe;AAAA,IACb,UAAU;AAAA,IACV,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,UAAU;AAAA,IACV,UAAU;AAAA,EACd;AACE,eAAa,WAAW;AACxB,eAAa,WAAW;AAAA,IACtB,UAAU;AAAA,IACV,UAAU;AAAA,EACd;AACE,SAAO;AACT;AACA,iBAAA,gBAAwB,SAAU,MAAM,QAAQ,UAAU;AACxD,MAAI,UACF,QAAQ,CAAE,GACV,MAAM;AACR,MAAI,QAAQ;AACV,SAAK,YAAa,WAAW,OAAO,QAAQ,MAAM,KAAK,OAAO,MAAM;AAClE,qBAAe,KAAK,QAAQ,QAAQ,KAClC,UAAU,YACV,aAAa,YACb,eAAe,aACd,MAAM,QAAQ,IAAI,OAAO,QAAQ;AACxC,MAAI,iBAAiB,UAAU,SAAS;AACxC,MAAI,MAAM;AAAgB,UAAM,WAAW;AAAA,WAClC,IAAI,gBAAgB;AAC3B,aAAS,aAAa,MAAM,cAAc,GAAG,IAAI,GAAG,IAAI,gBAAgB;AACtE,iBAAW,CAAC,IAAI,UAAU,IAAI,CAAC;AACjC,UAAM,WAAW;AAAA,EAClB;AACD,MAAI,QAAQ,KAAK;AACf,SAAK,YAAc,iBAAiB,KAAK,cAAe;AACtD,iBAAW,MAAM,QAAQ,MACtB,MAAM,QAAQ,IAAI,eAAe,QAAQ;AAChD,SAAO,aAAa,MAAM,KAAK,QAAQ,QAAQ,MAAM,KAAK;AAC5D;AACA,iBAAA,YAAoB,WAAY;AAC9B,SAAO,EAAE,SAAS;AACpB;AACkB,iBAAA,aAAG,SAAU,QAAQ;AACrC,SAAO,EAAE,UAAU,wBAAwB,OAAc;AAC3D;AACsB,iBAAA,iBAAG;AACb,iBAAA,OAAG,SAAU,MAAM;AAC7B,SAAO;AAAA,IACL,UAAU;AAAA,IACV,UAAU,EAAE,SAAS,IAAI,SAAS,KAAM;AAAA,IACxC,OAAO;AAAA,EACX;AACA;AACA,iBAAA,OAAe,SAAU,MAAM,SAAS;AACtC,SAAO;AAAA,IACL,UAAU;AAAA,IACV;AAAA,IACA,SAAS,WAAW,UAAU,OAAO;AAAA,EACzC;AACA;AACuB,iBAAA,kBAAG,SAAU,OAAO;AACzC,MAAI,iBAAiB,qBAAqB,GACxC,oBAAoB,CAAA;AACtB,uBAAqB,IAAI;AACzB,MAAI;AACF,QAAI,cAAc,MAAO,GACvB,0BAA0B,qBAAqB;AACjD,aAAS,2BACP,wBAAwB,mBAAmB,WAAW;AACxD,iBAAa,OAAO,eAClB,SAAS,eACT,eAAe,OAAO,YAAY,QAClC,YAAY,KAAK,MAAM,iBAAiB;AAAA,EAC3C,SAAQ,OAAO;AACd,sBAAkB,KAAK;AAAA,EAC3B,UAAY;AACR,yBAAqB,IAAI;AAAA,EAC1B;AACH;AACA,iBAAA,2BAAmC,WAAY;AAC7C,SAAO,qBAAqB,EAAE;AAChC;AACW,iBAAA,MAAG,SAAU,QAAQ;AAC9B,SAAO,qBAAqB,EAAE,IAAI,MAAM;AAC1C;AACA,iBAAA,iBAAyB,SAAU,QAAQ,cAAc,WAAW;AAClE,SAAO,qBAAqB,EAAE,eAAe,QAAQ,cAAc,SAAS;AAC9E;AACA,iBAAA,cAAsB,SAAU,UAAU,MAAM;AAC9C,SAAO,qBAAqB,EAAE,YAAY,UAAU,IAAI;AAC1D;AACkB,iBAAA,aAAG,SAAU,SAAS;AACtC,SAAO,qBAAqB,EAAE,WAAW,OAAO;AAClD;AACqB,iBAAA,gBAAG,WAAY;AAAG;AACvC,iBAAA,mBAA2B,SAAU,OAAO,cAAc;AACxD,SAAO,qBAAqB,EAAE,iBAAiB,OAAO,YAAY;AACpE;AACA,iBAAA,YAAoB,SAAU,QAAQ,YAAY,QAAQ;AACxD,MAAI,aAAa,qBAAqB;AACtC,MAAI,eAAe,OAAO;AACxB,UAAM;AAAA,MACJ;AAAA,IACN;AACE,SAAO,WAAW,UAAU,QAAQ,UAAU;AAChD;AACA,iBAAA,QAAgB,WAAY;AAC1B,SAAO,qBAAqB,EAAE;AAChC;AACA,iBAAA,sBAA8B,SAAU,KAAK,QAAQ,MAAM;AACzD,SAAO,qBAAqB,EAAE,oBAAoB,KAAK,QAAQ,IAAI;AACrE;AACA,iBAAA,qBAA6B,SAAU,QAAQ,MAAM;AACnD,SAAO,qBAAqB,EAAE,mBAAmB,QAAQ,IAAI;AAC/D;AACA,iBAAA,kBAA0B,SAAU,QAAQ,MAAM;AAChD,SAAO,qBAAqB,EAAE,gBAAgB,QAAQ,IAAI;AAC5D;AACA,iBAAA,UAAkB,SAAU,QAAQ,MAAM;AACxC,SAAO,qBAAqB,EAAE,QAAQ,QAAQ,IAAI;AACpD;AACA,iBAAA,gBAAwB,SAAU,aAAa,SAAS;AACtD,SAAO,qBAAqB,EAAE,cAAc,aAAa,OAAO;AAClE;AACA,iBAAA,aAAqB,SAAU,SAAS,YAAY,MAAM;AACxD,SAAO,qBAAqB,EAAE,WAAW,SAAS,YAAY,IAAI;AACpE;AACc,iBAAA,SAAG,SAAU,cAAc;AACvC,SAAO,qBAAqB,EAAE,OAAO,YAAY;AACnD;AACgB,iBAAA,WAAG,SAAU,cAAc;AACzC,SAAO,qBAAqB,EAAE,SAAS,YAAY;AACrD;AACA,iBAAA,uBAA+B,SAC7B,WACA,aACA,mBACA;AACA,SAAO,qBAAqB,EAAE;AAAA,IAC5B;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACA;AACA,iBAAA,gBAAwB,WAAY;AAClC,SAAO,qBAAqB,EAAE;AAChC;AACA,iBAAA,UAAkB;AC/hByB;AAClCC,QAAA,UAAUC;AACnB;;;", "x_google_ignoreList": [0, 1]}