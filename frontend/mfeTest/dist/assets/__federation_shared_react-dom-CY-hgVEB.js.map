{"version": 3, "file": "__federation_shared_react-dom-CY-hgVEB.js", "sources": ["../../node_modules/react-dom/cjs/react-dom.production.js", "../../node_modules/react-dom/index.js"], "sourcesContent": ["/**\n * @license React\n * react-dom.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar React = require(\"react\");\nfunction formatProdErrorMessage(code) {\n  var url = \"https://react.dev/errors/\" + code;\n  if (1 < arguments.length) {\n    url += \"?args[]=\" + encodeURIComponent(arguments[1]);\n    for (var i = 2; i < arguments.length; i++)\n      url += \"&args[]=\" + encodeURIComponent(arguments[i]);\n  }\n  return (\n    \"Minified React error #\" +\n    code +\n    \"; visit \" +\n    url +\n    \" for the full message or use the non-minified dev environment for full errors and additional helpful warnings.\"\n  );\n}\nfunction noop() {}\nvar Internals = {\n    d: {\n      f: noop,\n      r: function () {\n        throw Error(formatProdErrorMessage(522));\n      },\n      D: noop,\n      C: noop,\n      L: noop,\n      m: noop,\n      X: noop,\n      S: noop,\n      M: noop\n    },\n    p: 0,\n    findDOMNode: null\n  },\n  REACT_PORTAL_TYPE = Symbol.for(\"react.portal\");\nfunction createPortal$1(children, containerInfo, implementation) {\n  var key =\n    3 < arguments.length && void 0 !== arguments[3] ? arguments[3] : null;\n  return {\n    $$typeof: REACT_PORTAL_TYPE,\n    key: null == key ? null : \"\" + key,\n    children: children,\n    containerInfo: containerInfo,\n    implementation: implementation\n  };\n}\nvar ReactSharedInternals =\n  React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;\nfunction getCrossOriginStringAs(as, input) {\n  if (\"font\" === as) return \"\";\n  if (\"string\" === typeof input)\n    return \"use-credentials\" === input ? input : \"\";\n}\nexports.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE =\n  Internals;\nexports.createPortal = function (children, container) {\n  var key =\n    2 < arguments.length && void 0 !== arguments[2] ? arguments[2] : null;\n  if (\n    !container ||\n    (1 !== container.nodeType &&\n      9 !== container.nodeType &&\n      11 !== container.nodeType)\n  )\n    throw Error(formatProdErrorMessage(299));\n  return createPortal$1(children, container, null, key);\n};\nexports.flushSync = function (fn) {\n  var previousTransition = ReactSharedInternals.T,\n    previousUpdatePriority = Internals.p;\n  try {\n    if (((ReactSharedInternals.T = null), (Internals.p = 2), fn)) return fn();\n  } finally {\n    (ReactSharedInternals.T = previousTransition),\n      (Internals.p = previousUpdatePriority),\n      Internals.d.f();\n  }\n};\nexports.preconnect = function (href, options) {\n  \"string\" === typeof href &&\n    (options\n      ? ((options = options.crossOrigin),\n        (options =\n          \"string\" === typeof options\n            ? \"use-credentials\" === options\n              ? options\n              : \"\"\n            : void 0))\n      : (options = null),\n    Internals.d.C(href, options));\n};\nexports.prefetchDNS = function (href) {\n  \"string\" === typeof href && Internals.d.D(href);\n};\nexports.preinit = function (href, options) {\n  if (\"string\" === typeof href && options && \"string\" === typeof options.as) {\n    var as = options.as,\n      crossOrigin = getCrossOriginStringAs(as, options.crossOrigin),\n      integrity =\n        \"string\" === typeof options.integrity ? options.integrity : void 0,\n      fetchPriority =\n        \"string\" === typeof options.fetchPriority\n          ? options.fetchPriority\n          : void 0;\n    \"style\" === as\n      ? Internals.d.S(\n          href,\n          \"string\" === typeof options.precedence ? options.precedence : void 0,\n          {\n            crossOrigin: crossOrigin,\n            integrity: integrity,\n            fetchPriority: fetchPriority\n          }\n        )\n      : \"script\" === as &&\n        Internals.d.X(href, {\n          crossOrigin: crossOrigin,\n          integrity: integrity,\n          fetchPriority: fetchPriority,\n          nonce: \"string\" === typeof options.nonce ? options.nonce : void 0\n        });\n  }\n};\nexports.preinitModule = function (href, options) {\n  if (\"string\" === typeof href)\n    if (\"object\" === typeof options && null !== options) {\n      if (null == options.as || \"script\" === options.as) {\n        var crossOrigin = getCrossOriginStringAs(\n          options.as,\n          options.crossOrigin\n        );\n        Internals.d.M(href, {\n          crossOrigin: crossOrigin,\n          integrity:\n            \"string\" === typeof options.integrity ? options.integrity : void 0,\n          nonce: \"string\" === typeof options.nonce ? options.nonce : void 0\n        });\n      }\n    } else null == options && Internals.d.M(href);\n};\nexports.preload = function (href, options) {\n  if (\n    \"string\" === typeof href &&\n    \"object\" === typeof options &&\n    null !== options &&\n    \"string\" === typeof options.as\n  ) {\n    var as = options.as,\n      crossOrigin = getCrossOriginStringAs(as, options.crossOrigin);\n    Internals.d.L(href, as, {\n      crossOrigin: crossOrigin,\n      integrity:\n        \"string\" === typeof options.integrity ? options.integrity : void 0,\n      nonce: \"string\" === typeof options.nonce ? options.nonce : void 0,\n      type: \"string\" === typeof options.type ? options.type : void 0,\n      fetchPriority:\n        \"string\" === typeof options.fetchPriority\n          ? options.fetchPriority\n          : void 0,\n      referrerPolicy:\n        \"string\" === typeof options.referrerPolicy\n          ? options.referrerPolicy\n          : void 0,\n      imageSrcSet:\n        \"string\" === typeof options.imageSrcSet ? options.imageSrcSet : void 0,\n      imageSizes:\n        \"string\" === typeof options.imageSizes ? options.imageSizes : void 0,\n      media: \"string\" === typeof options.media ? options.media : void 0\n    });\n  }\n};\nexports.preloadModule = function (href, options) {\n  if (\"string\" === typeof href)\n    if (options) {\n      var crossOrigin = getCrossOriginStringAs(options.as, options.crossOrigin);\n      Internals.d.m(href, {\n        as:\n          \"string\" === typeof options.as && \"script\" !== options.as\n            ? options.as\n            : void 0,\n        crossOrigin: crossOrigin,\n        integrity:\n          \"string\" === typeof options.integrity ? options.integrity : void 0\n      });\n    } else Internals.d.m(href);\n};\nexports.requestFormReset = function (form) {\n  Internals.d.r(form);\n};\nexports.unstable_batchedUpdates = function (fn, a) {\n  return fn(a);\n};\nexports.useFormState = function (action, initialState, permalink) {\n  return ReactSharedInternals.H.useFormState(action, initialState, permalink);\n};\nexports.useFormStatus = function () {\n  return ReactSharedInternals.H.useHostTransitionStatus();\n};\nexports.version = \"19.1.0\";\n", "'use strict';\n\nfunction checkDCE() {\n  /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n  if (\n    typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ === 'undefined' ||\n    typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE !== 'function'\n  ) {\n    return;\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    // This branch is unreachable because this function is only called\n    // in production, but the condition is true only in development.\n    // Therefore if the branch is still here, dead code elimination wasn't\n    // properly applied.\n    // Don't change the message. React DevTools relies on it. Also make sure\n    // this message doesn't occur elsewhere in this function, or it will cause\n    // a false positive.\n    throw new Error('^_^');\n  }\n  try {\n    // Verify that the code above has been dead code eliminated (DCE'd).\n    __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(checkDCE);\n  } catch (err) {\n    // DevTools shouldn't crash React, no matter what.\n    // We should still report in case we break this code.\n    console.error(err);\n  }\n}\n\nif (process.env.NODE_ENV === 'production') {\n  // DCE check should happen before ReactDOM bundle executes so that\n  // DevTools can report bad minification during injection.\n  checkDCE();\n  module.exports = require('./cjs/react-dom.production.js');\n} else {\n  module.exports = require('./cjs/react-dom.development.js');\n}\n"], "names": ["require$$0", "reactDomModule"], "mappings": ";;;;;;;;;;;;AAWA,IAAI,QAAQA;AACZ,SAAS,uBAAuB,MAAM;AACpC,MAAI,MAAM,8BAA8B;AACxC,MAAI,IAAI,UAAU,QAAQ;AACxB,WAAO,aAAa,mBAAmB,UAAU,CAAC,CAAC;AACnD,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ;AACpC,aAAO,aAAa,mBAAmB,UAAU,CAAC,CAAC;AAAA,EACtD;AACD,SACE,2BACA,OACA,aACA,MACA;AAEJ;AACA,SAAS,OAAO;AAAE;AAClB,IAAI,YAAY;AAAA,EACZ,GAAG;AAAA,IACD,GAAG;AAAA,IACH,GAAG,WAAY;AACb,YAAM,MAAM,uBAAuB,GAAG,CAAC;AAAA,IACxC;AAAA,IACD,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACJ;AAAA,EACD,GAAG;AAAA,EACH,aAAa;AACd,GACD,oBAAoB,OAAO,IAAI,cAAc;AAC/C,SAAS,eAAe,UAAU,eAAe,gBAAgB;AAC/D,MAAI,MACF,IAAI,UAAU,UAAU,WAAW,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI;AACnE,SAAO;AAAA,IACL,UAAU;AAAA,IACV,KAAK,QAAQ,MAAM,OAAO,KAAK;AAAA,IAC/B;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACA;AACA,IAAI,uBACF,MAAM;AACR,SAAS,uBAAuB,IAAI,OAAO;AACzC,MAAI,WAAW;AAAI,WAAO;AAC1B,MAAI,aAAa,OAAO;AACtB,WAAO,sBAAsB,QAAQ,QAAQ;AACjD;AACoE,oBAAA,+DAClE;AACF,oBAAA,eAAuB,SAAU,UAAU,WAAW;AACpD,MAAI,MACF,IAAI,UAAU,UAAU,WAAW,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI;AACnE,MACE,CAAC,aACA,MAAM,UAAU,YACf,MAAM,UAAU,YAChB,OAAO,UAAU;AAEnB,UAAM,MAAM,uBAAuB,GAAG,CAAC;AACzC,SAAO,eAAe,UAAU,WAAW,MAAM,GAAG;AACtD;AACiB,oBAAA,YAAG,SAAU,IAAI;AAChC,MAAI,qBAAqB,qBAAqB,GAC5C,yBAAyB,UAAU;AACrC,MAAI;AACF,QAAM,qBAAqB,IAAI,MAAQ,UAAU,IAAI,GAAI;AAAK,aAAO;EACzE,UAAY;AACR,IAAC,qBAAqB,IAAI,oBACvB,UAAU,IAAI,wBACf,UAAU,EAAE;EACf;AACH;AACA,oBAAA,aAAqB,SAAU,MAAM,SAAS;AAC5C,eAAa,OAAO,SACjB,WACK,UAAU,QAAQ,aACnB,UACC,aAAa,OAAO,UAChB,sBAAsB,UACpB,UACA,KACF,UACL,UAAU,MACf,UAAU,EAAE,EAAE,MAAM,OAAO;AAC/B;AACmB,oBAAA,cAAG,SAAU,MAAM;AACpC,eAAa,OAAO,QAAQ,UAAU,EAAE,EAAE,IAAI;AAChD;AACA,oBAAA,UAAkB,SAAU,MAAM,SAAS;AACzC,MAAI,aAAa,OAAO,QAAQ,WAAW,aAAa,OAAO,QAAQ,IAAI;AACzE,QAAI,KAAK,QAAQ,IACf,cAAc,uBAAuB,IAAI,QAAQ,WAAW,GAC5D,YACE,aAAa,OAAO,QAAQ,YAAY,QAAQ,YAAY,QAC9D,gBACE,aAAa,OAAO,QAAQ,gBACxB,QAAQ,gBACR;AACR,gBAAY,KACR,UAAU,EAAE;AAAA,MACV;AAAA,MACA,aAAa,OAAO,QAAQ,aAAa,QAAQ,aAAa;AAAA,MAC9D;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,IACF,IACD,aAAa,MACb,UAAU,EAAE,EAAE,MAAM;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO,aAAa,OAAO,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,IACrE,CAAS;AAAA,EACN;AACH;AACA,oBAAA,gBAAwB,SAAU,MAAM,SAAS;AAC/C,MAAI,aAAa,OAAO;AACtB,QAAI,aAAa,OAAO,WAAW,SAAS,SAAS;AACnD,UAAI,QAAQ,QAAQ,MAAM,aAAa,QAAQ,IAAI;AACjD,YAAI,cAAc;AAAA,UAChB,QAAQ;AAAA,UACR,QAAQ;AAAA,QAClB;AACQ,kBAAU,EAAE,EAAE,MAAM;AAAA,UAClB;AAAA,UACA,WACE,aAAa,OAAO,QAAQ,YAAY,QAAQ,YAAY;AAAA,UAC9D,OAAO,aAAa,OAAO,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,QACrE,CAAS;AAAA,MACF;AAAA,IACP;AAAW,cAAQ,WAAW,UAAU,EAAE,EAAE,IAAI;AAChD;AACA,oBAAA,UAAkB,SAAU,MAAM,SAAS;AACzC,MACE,aAAa,OAAO,QACpB,aAAa,OAAO,WACpB,SAAS,WACT,aAAa,OAAO,QAAQ,IAC5B;AACA,QAAI,KAAK,QAAQ,IACf,cAAc,uBAAuB,IAAI,QAAQ,WAAW;AAC9D,cAAU,EAAE,EAAE,MAAM,IAAI;AAAA,MACtB;AAAA,MACA,WACE,aAAa,OAAO,QAAQ,YAAY,QAAQ,YAAY;AAAA,MAC9D,OAAO,aAAa,OAAO,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,MAC3D,MAAM,aAAa,OAAO,QAAQ,OAAO,QAAQ,OAAO;AAAA,MACxD,eACE,aAAa,OAAO,QAAQ,gBACxB,QAAQ,gBACR;AAAA,MACN,gBACE,aAAa,OAAO,QAAQ,iBACxB,QAAQ,iBACR;AAAA,MACN,aACE,aAAa,OAAO,QAAQ,cAAc,QAAQ,cAAc;AAAA,MAClE,YACE,aAAa,OAAO,QAAQ,aAAa,QAAQ,aAAa;AAAA,MAChE,OAAO,aAAa,OAAO,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,IACjE,CAAK;AAAA,EACF;AACH;AACA,oBAAA,gBAAwB,SAAU,MAAM,SAAS;AAC/C,MAAI,aAAa,OAAO;AACtB,QAAI,SAAS;AACX,UAAI,cAAc,uBAAuB,QAAQ,IAAI,QAAQ,WAAW;AACxE,gBAAU,EAAE,EAAE,MAAM;AAAA,QAClB,IACE,aAAa,OAAO,QAAQ,MAAM,aAAa,QAAQ,KACnD,QAAQ,KACR;AAAA,QACN;AAAA,QACA,WACE,aAAa,OAAO,QAAQ,YAAY,QAAQ,YAAY;AAAA,MACtE,CAAO;AAAA,IACF;AAAM,gBAAU,EAAE,EAAE,IAAI;AAC7B;AACwB,oBAAA,mBAAG,SAAU,MAAM;AACzC,YAAU,EAAE,EAAE,IAAI;AACpB;AACA,oBAAA,0BAAkC,SAAU,IAAI,GAAG;AACjD,SAAO,GAAG,CAAC;AACb;AACA,oBAAA,eAAuB,SAAU,QAAQ,cAAc,WAAW;AAChE,SAAO,qBAAqB,EAAE,aAAa,QAAQ,cAAc,SAAS;AAC5E;AACA,oBAAA,gBAAwB,WAAY;AAClC,SAAO,qBAAqB,EAAE;AAChC;AACA,oBAAA,UAAkB;AC/MlB,SAAS,WAAW;AAElB,MACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,aAAa,YACnD;AACA;AAAA,EACF;AAWI,MAAA;AAEF,mCAA+B,SAAS,QAAQ;AAAA,WACzC,KAAK;AAGZ,YAAQ,MAAM,GAAG;AAAA,EACnB;AACF;AAE2C;AAGhC;AACFC,WAAA,UAAUD;AACnB;;;", "x_google_ignoreList": [0, 1]}