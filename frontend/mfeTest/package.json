{"name": "app_mfe-test", "private": true, "version": "1.0.0", "type": "module", "homepage": "https://tambia1.github.io", "scripts": {"start": "vite --host --port 5001 --strictPort", "build": "tsc && vite build", "preview": "tsc && vite build && vite preview --port 5001 --strictPort", "test": "vitest", "coverage": "vitest watch --coverage", "deploy": "gh-pages -d ./dist", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@tanstack/react-query": "5.24.1", "@v/shared-ui": "file:../@v/shared-ui", "i18next": "23.10.0", "i18next-browser-languagedetector": "7.2.0", "localforage": "1.10.0", "match-sorter": "6.3.4", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "14.0.5", "react-modal": "3.16.1", "react-router-dom": "6.22.1", "react-svg": "16.1.33", "styled-components": "5.3.6", "zustand": "4.4.7"}, "devDependencies": {"@originjs/vite-plugin-federation": "^1.3.5", "@testing-library/jest-dom": "6.4.2", "@testing-library/react": "14.2.1", "@types/node": "20.11.20", "@types/react": "18.2.60", "@types/react-dom": "18.2.19", "@types/styled-components": "5.1.34", "@typescript-eslint/eslint-plugin": "7.1.0", "@typescript-eslint/parser": "7.1.0", "@vitejs/plugin-react": "4.2.1", "@vitest/coverage-v8": "1.3.1", "babel-plugin-styled-components": "2.1.4", "dotenv": "16.4.5", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-react": "7.33.2", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-react-refresh": "0.4.5", "gh-pages": "6.1.1", "jsdom": "24.0.0", "styled-components": "6.1.8", "typescript": "5.3.3", "vite": "5.1.4", "vite-plugin-checker": "0.6.4", "vitest": "1.3.1", "zustand": "4.5.1"}}